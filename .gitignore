# Hugo build output
public/
resources/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Node modules (if using npm for build tools)
node_modules/

# Environment files
.env
.env.local
.idea

# API Configuration files (contain sensitive API keys)
config.json
**/config.json
projects/**/config.json

# Firebase cache and logs (but keep configuration files)
.firebase/
firebase-debug.log
ui-debug.log

# Note: firebase.json, .firebaserc, and deployment-config.json are kept in repo
# as they don't contain sensitive credentials (Firebase Hosting keys are public)
