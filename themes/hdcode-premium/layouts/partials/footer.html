<footer class="premium-footer">
    <div class="footer-container">
        <div class="footer-content">
            <div class="footer-brand">
                <div class="footer-logo">
                    <span class="logo-text">{{ .Site.Title }}</span>
                    <span class="logo-accent">.</span>
                </div>
                <p class="footer-description">
                    {{ .Site.Params.description }}
                </p>
                <div class="footer-social">
                    <a href="mailto:{{ .Site.Params.email }}" class="social-link">
                        <i class="fas fa-envelope"></i>
                    </a>
                    {{ if .Site.Params.footer.show_github }}
                    <a href="{{ .Site.Params.footer.github_url }}" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                    {{ end }}
                    {{ if .Site.Params.footer.show_linkedin }}
                    <a href="{{ .Site.Params.footer.linkedin_url }}" class="social-link">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    {{ end }}
                    {{ if .Site.Params.footer.show_twitter }}
                    <a href="{{ .Site.Params.footer.twitter_url }}" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    {{ end }}
                </div>
            </div>
            
            <div class="footer-links">
                <div class="footer-column">
                    <h4 class="footer-title">{{ .Site.Params.footer.services_title }}</h4>
                    <ul class="footer-list">
                        {{ range .Site.Params.footer.services }}
                        <li><a href="{{ .url }}">{{ .name }}</a></li>
                        {{ end }}
                    </ul>
                </div>

                <div class="footer-column">
                    <h4 class="footer-title">{{ .Site.Params.footer.company_title }}</h4>
                    <ul class="footer-list">
                        {{ range .Site.Params.footer.company }}
                        <li><a href="{{ .url }}">{{ .name }}</a></li>
                        {{ end }}
                    </ul>
                </div>

                {{ if .Site.Params.footer.show_projects }}
                <div class="footer-column">
                    <h4 class="footer-title">{{ .Site.Params.footer.projects_title }}</h4>
                    <ul class="footer-list">
                        {{ range .Site.Params.footer.projects }}
                        <li><a href="{{ .url }}" title="{{ .description }}">{{ .name }}</a></li>
                        {{ end }}
                    </ul>
                </div>
                {{ end }}

                <div class="footer-column">
                    <h4 class="footer-title">{{ .Site.Params.footer.contact_title }}</h4>
                    <ul class="footer-list">
                        <li>{{ .Site.Params.email }}</li>
                        <li>{{ .Site.Params.phone }}</li>
                        <li>{{ .Site.Params.footer.availability_text }}</li>
                        <li>{{ .Site.Params.footer.location_text }}</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="footer-copyright">
                <p>&copy; {{ now.Format "2006" }} {{ .Site.Title }}. All rights reserved.</p>
            </div>
            <div class="footer-legal">
                <a href="{{ .Site.Params.footer.privacy_url }}">{{ .Site.Params.footer.privacy_text }}</a>
                <a href="{{ .Site.Params.footer.terms_url }}">{{ .Site.Params.footer.terms_text }}</a>
            </div>
        </div>
    </div>
</footer>

<style>
.premium-footer {
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-3xl) 0 var(--spacing-lg) 0;
    margin-top: var(--spacing-3xl);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.footer-logo .logo-text {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-logo .logo-accent {
    color: var(--accent-primary);
    font-size: 2rem;
    margin-left: 2px;
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--gradient-primary);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
}

.footer-title {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.footer-list {
    list-style: none;
}

.footer-list li {
    margin-bottom: var(--spacing-xs);
}

.footer-list a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-list a:hover {
    color: var(--accent-primary);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.footer-legal {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-legal a {
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color var(--transition-normal);
}

.footer-legal a:hover {
    color: var(--accent-primary);
}

@media (max-width: 1024px) {
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
}
</style>
