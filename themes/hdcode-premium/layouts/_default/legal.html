{{ define "main" }}
<article class="legal-page">
    <div class="legal-container">
        <header class="legal-header">
            <h1 class="legal-title">{{ .Title }}</h1>
            {{ if .Params.description }}
            <p class="legal-description">{{ .Params.description }}</p>
            {{ end }}
        </header>
        
        <div class="legal-content">
            {{ .Content }}
        </div>
        
        <footer class="legal-footer">
            <div class="legal-contact">
                <p>Questions about this {{ .Title | lower }}? <a href="mailto:{{ .Site.Params.email }}">Contact us</a></p>
            </div>
        </footer>
    </div>
</article>

<style>
.legal-page {
    padding: var(--spacing-3xl) 0;
    min-height: 100vh;
    background: var(--bg-primary);
}

.legal-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.legal-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    padding-top: 100px;
}

.legal-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.legal-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.legal-content {
    line-height: 1.8;
    color: var(--text-secondary);
    font-size: 1rem;
}

.legal-content h1 {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: var(--spacing-2xl) 0 var(--spacing-lg) 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.legal-content h2 {
    font-family: var(--font-display);
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-xl) 0 var(--spacing-md) 0;
    border-bottom: 2px solid rgba(99, 102, 241, 0.3);
    padding-bottom: var(--spacing-xs);
}

.legal-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
}

.legal-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

.legal-content p {
    margin-bottom: var(--spacing-md);
    text-align: justify;
}

.legal-content ul,
.legal-content ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-xl);
}

.legal-content li {
    margin-bottom: var(--spacing-xs);
}

.legal-content strong {
    color: var(--text-primary);
    font-weight: 600;
}

.legal-content em {
    color: var(--accent-primary);
    font-style: italic;
}

.legal-content a {
    color: var(--accent-primary);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all var(--transition-normal);
}

.legal-content a:hover {
    border-bottom-color: var(--accent-primary);
    color: var(--accent-secondary);
}

.legal-content code {
    background: var(--bg-glass);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    color: var(--accent-primary);
    font-family: 'Monaco', 'Menlo', monospace;
}

.legal-content blockquote {
    background: var(--bg-glass);
    border-left: 4px solid var(--accent-primary);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
    font-style: italic;
}

.legal-content hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    margin: var(--spacing-xl) 0;
}

.legal-footer {
    margin-top: var(--spacing-3xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.legal-contact {
    background: var(--bg-glass);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.legal-contact p {
    margin: 0;
    color: var(--text-secondary);
}

.legal-contact a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-normal);
}

.legal-contact a:hover {
    color: var(--accent-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .legal-container {
        padding: 0 var(--spacing-md);
    }
    
    .legal-header {
        padding-top: 80px;
    }
    
    .legal-content {
        font-size: 0.95rem;
    }
    
    .legal-content h1 {
        font-size: 2rem;
    }
    
    .legal-content h2 {
        font-size: 1.5rem;
    }
    
    .legal-content h3 {
        font-size: 1.25rem;
    }
    
    .legal-content p {
        text-align: left;
    }
    
    .legal-content ul,
    .legal-content ol {
        padding-left: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .legal-content {
        font-size: 0.9rem;
    }
    
    .legal-content ul,
    .legal-content ol {
        padding-left: var(--spacing-md);
    }
}
</style>
{{ end }}
