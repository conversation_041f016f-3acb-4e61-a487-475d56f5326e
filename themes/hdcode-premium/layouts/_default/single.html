{{ define "main" }}
<article class="single-post">
    <div class="post-container">
        <header class="post-header">
            <div class="post-meta">
                <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "January 2, 2006" }}</time>
                {{ if .Params.tags }}
                <div class="post-tags">
                    {{ range .Params.tags }}
                    <span class="tag">{{ . }}</span>
                    {{ end }}
                </div>
                {{ end }}
            </div>
            <h1 class="post-title">{{ .Title }}</h1>
            {{ if .Params.description }}
            <p class="post-description">{{ .Params.description }}</p>
            {{ end }}
        </header>
        
        <div class="post-content">
            {{ .Content }}
        </div>
        
        <footer class="post-footer">
            <div class="post-share">
                <h4>{{ .Site.Params.blog.share_text }}</h4>
                <div class="share-buttons">
                    <a href="https://twitter.com/intent/tweet?text={{ .Title }}&url={{ .Permalink }}" target="_blank" class="share-button twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ .Permalink }}" target="_blank" class="share-button linkedin">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="mailto:?subject={{ .Title }}&body={{ .Permalink }}" class="share-button email">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
        </footer>
    </div>
</article>

<style>
.single-post {
    padding: var(--spacing-3xl) 0;
    min-height: 100vh;
}

.post-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.post-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    padding-top: 80px;
}

.post-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.post-tags {
    display: flex;
    gap: var(--spacing-xs);
}

.tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
    color: var(--accent-primary);
}

.post-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.post-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.post-content {
    line-height: 1.8;
    color: var(--text-secondary);
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
    color: var(--text-primary);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
}

.post-content p {
    margin-bottom: var(--spacing-md);
}

.post-content ul,
.post-content ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.post-content li {
    margin-bottom: var(--spacing-xs);
}

.post-content a {
    color: var(--accent-primary);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color var(--transition-normal);
}

.post-content a:hover {
    border-bottom-color: var(--accent-primary);
}

.post-content code {
    background: var(--bg-glass);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    color: var(--accent-primary);
}

.post-content pre {
    background: var(--bg-glass);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
    margin: var(--spacing-md) 0;
}

.post-footer {
    margin-top: var(--spacing-3xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.post-share h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.share-buttons {
    display: flex;
    gap: var(--spacing-md);
}

.share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.share-button:hover {
    background: var(--gradient-primary);
    color: var(--text-primary);
    transform: translateY(-2px);
}
</style>
{{ end }}
