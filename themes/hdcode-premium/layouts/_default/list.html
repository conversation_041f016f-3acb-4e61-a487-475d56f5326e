{{ define "main" }}
<section class="list-page">
    <div class="list-container">
        <header class="list-header">
            <h1 class="list-title">{{ .Title }}</h1>
            {{ if .Content }}
            <div class="list-description">
                {{ .Content }}
            </div>
            {{ end }}
        </header>
        
        {{ if .Pages }}
        <div class="posts-grid">
            {{ range .Pages }}
            <article class="post-card">
                <div class="post-card-content">
                    <div class="post-meta">
                        <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "Jan 2, 2006" }}</time>
                        {{ if .Params.tags }}
                        <div class="post-tags">
                            {{ range first 2 .Params.tags }}
                            <span class="tag">{{ . }}</span>
                            {{ end }}
                        </div>
                        {{ end }}
                    </div>
                    
                    <h2 class="post-card-title">
                        <a href="{{ .Permalink }}">{{ .Title }}</a>
                    </h2>
                    
                    {{ if .Params.description }}
                    <p class="post-card-description">{{ .Params.description }}</p>
                    {{ else }}
                    <p class="post-card-description">{{ .Summary }}</p>
                    {{ end }}
                    
                    <a href="{{ .Permalink }}" class="read-more">
                        <span>{{ .Site.Params.blog.read_more_text }}</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </article>
            {{ end }}
        </div>
        {{ else }}
        <div class="empty-state">
            <i class="fas fa-file-alt"></i>
            <h3>{{ .Site.Params.blog.no_posts_title }}</h3>
            <p>{{ .Site.Params.blog.no_posts_description }}</p>
        </div>
        {{ end }}
    </div>
</section>

<style>
.list-page {
    padding: var(--spacing-3xl) 0;
    min-height: 100vh;
}

.list-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.list-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    padding-top: 80px;
}

.list-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.list-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.post-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-xl);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.post-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.post-card:hover::before {
    transform: scaleX(1);
}

.post-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-premium);
}

.post-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.post-tags {
    display: flex;
    gap: var(--spacing-xs);
}

.tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
    color: var(--accent-primary);
}

.post-card-title {
    margin-bottom: var(--spacing-md);
}

.post-card-title a {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.post-card-title a:hover {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.post-card-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.read-more {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all var(--transition-normal);
}

.read-more:hover {
    gap: var(--spacing-sm);
    color: var(--accent-secondary);
}

.read-more i {
    transition: transform var(--transition-normal);
}

.read-more:hover i {
    transform: translateX(3px);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-3xl) 0;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .posts-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}
</style>
{{ end }}
