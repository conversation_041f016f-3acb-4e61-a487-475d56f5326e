{"hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(eot|otf|ttf|ttc|woff|font.css)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=2592000"}]}, {"source": "**/*.@(html|xml|txt|json)", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}]}], "cleanUrls": true, "trailingSlash": false}}