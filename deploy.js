#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration
let config;
try {
  config = JSON.parse(fs.readFileSync('deployment-config.json', 'utf8'));
} catch (error) {
  console.error(`${colors.red}❌ Error reading deployment-config.json:${colors.reset}`, error.message);
  process.exit(1);
}

// Parse command line arguments
const args = process.argv.slice(2);
const skipBuild = args.includes('--skip-build') || config.deployment.skipBuild;
const customMessage = args.find(arg => arg.startsWith('--message='))?.split('=')[1];

// Helper functions
function log(message, color = colors.reset) {
  const timestamp = config.notifications.showTimestamp ? 
    `${colors.cyan}[${new Date().toLocaleTimeString()}]${colors.reset} ` : '';
  console.log(`${timestamp}${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`${colors.bright}${step}${colors.reset} ${message}`, colors.blue);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function executeCommand(command, description, options = {}) {
  try {
    logStep('🔄', `${description}...`);
    
    const result = execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
    
    if (config.notifications.showBuildStatus) {
      logSuccess(`${description} completed successfully`);
    }
    
    return result;
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    if (error.stdout) {
      console.log('STDOUT:', error.stdout);
    }
    if (error.stderr) {
      console.log('STDERR:', error.stderr);
    }
    throw error;
  }
}

function checkPrerequisites() {
  logStep('🔍', 'Checking prerequisites...');
  
  // Check if Hugo is installed
  try {
    executeCommand('hugo version', 'Checking Hugo installation', { silent: true });
  } catch (error) {
    logError('Hugo is not installed or not in PATH');
    log('Please install Hugo: https://gohugo.io/installation/', colors.yellow);
    process.exit(1);
  }
  
  // Check if Firebase CLI is available
  try {
    executeCommand('npx firebase --version', 'Checking Firebase CLI', { silent: true });
  } catch (error) {
    logError('Firebase CLI is not available');
    log('Installing Firebase CLI...', colors.yellow);
    try {
      executeCommand('npm install', 'Installing dependencies');
    } catch (installError) {
      logError('Failed to install Firebase CLI');
      process.exit(1);
    }
  }
  
  // Check if user is logged in to Firebase
  try {
    executeCommand('npx firebase projects:list', 'Checking Firebase authentication', { silent: true });
  } catch (error) {
    logError('Not authenticated with Firebase');
    log('Please run: npm run firebase:login', colors.yellow);
    process.exit(1);
  }
  
  logSuccess('All prerequisites met');
}

function cleanBuild() {
  if (config.deployment.cleanBuild && fs.existsSync(config.hugo.publicDir)) {
    logStep('🧹', 'Cleaning previous build...');
    try {
      executeCommand(`rm -rf ${config.hugo.publicDir}/*`, 'Cleaning public directory');
      logSuccess('Previous build cleaned');
    } catch (error) {
      logWarning('Could not clean previous build, continuing...');
    }
  }
}

function buildSite() {
  if (skipBuild) {
    logWarning('Skipping Hugo build (--skip-build flag used)');
    return;
  }
  
  logStep('🏗️', 'Building Hugo site...');
  executeCommand(config.hugo.buildCommand, 'Building site with Hugo');
  
  // Verify build output
  if (!fs.existsSync(config.hugo.publicDir)) {
    logError(`Build directory ${config.hugo.publicDir} not found after build`);
    process.exit(1);
  }
  
  const files = fs.readdirSync(config.hugo.publicDir);
  if (files.length === 0) {
    logError('Build directory is empty');
    process.exit(1);
  }
  
  logSuccess(`Site built successfully (${files.length} files/directories)`);
}

function deployToFirebase() {
  logStep('🚀', 'Deploying to Firebase Hosting...');
  
  const deployMessage = customMessage || config.deployment.message;
  const command = `npx firebase deploy --only hosting --message "${deployMessage}"`;
  
  executeCommand(command, 'Deploying to Firebase Hosting');
  
  if (config.notifications.showDeploymentUrl) {
    log('', colors.green);
    log('🎉 Deployment completed successfully!', colors.green);
    log(`🌐 Your site is live at: ${colors.bright}${config.hugo.baseURL}${colors.reset}`, colors.green);
    log('', colors.green);
  }
}

// Main deployment process
async function deploy() {
  const startTime = Date.now();
  
  log('', colors.cyan);
  log('🚀 HDCode.dev Firebase Deployment', colors.bright);
  log('=====================================', colors.cyan);
  log('', colors.cyan);
  
  try {
    checkPrerequisites();
    cleanBuild();
    buildSite();
    deployToFirebase();
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    log('', colors.green);
    log(`✨ Deployment completed in ${duration}s`, colors.green);
    log('', colors.green);
    
  } catch (error) {
    log('', colors.red);
    logError('Deployment failed!');
    log('', colors.red);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('', colors.yellow);
  logWarning('Deployment interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('', colors.yellow);
  logWarning('Deployment terminated');
  process.exit(1);
});

// Run deployment
deploy();
