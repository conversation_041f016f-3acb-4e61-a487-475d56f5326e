{"name": "hdcode-dev-deployment", "version": "1.0.0", "description": "Firebase deployment configuration for HDCode.dev Hugo site", "private": true, "scripts": {"deploy": "node deploy.js", "deploy:quick": "node deploy.js --skip-build", "build": "hugo --minify", "serve": "hugo server -D", "firebase:login": "firebase login", "firebase:logout": "firebase logout", "firebase:projects": "firebase projects:list", "firebase:hosting": "firebase hosting:sites:list"}, "devDependencies": {"firebase-tools": "^13.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "private"}, "author": "HDCode", "license": "UNLICENSED"}