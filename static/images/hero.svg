<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d3ee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#84cc16;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#grad2)"/>
  
  <!-- Code editor window -->
  <rect x="50" y="50" width="300" height="200" rx="10" fill="#1e293b" stroke="#22d3ee" stroke-width="2"/>
  
  <!-- Window controls -->
  <circle cx="70" cy="70" r="5" fill="#ef4444"/>
  <circle cx="90" cy="70" r="5" fill="#f59e0b"/>
  <circle cx="110" cy="70" r="5" fill="#10b981"/>
  
  <!-- Code lines -->
  <rect x="70" y="90" width="80" height="4" fill="#22d3ee" opacity="0.8"/>
  <rect x="70" y="105" width="120" height="4" fill="#84cc16" opacity="0.8"/>
  <rect x="70" y="120" width="60" height="4" fill="#22d3ee" opacity="0.6"/>
  <rect x="70" y="135" width="140" height="4" fill="#84cc16" opacity="0.6"/>
  <rect x="70" y="150" width="90" height="4" fill="#22d3ee" opacity="0.8"/>
  <rect x="70" y="165" width="110" height="4" fill="#84cc16" opacity="0.8"/>
  <rect x="70" y="180" width="70" height="4" fill="#22d3ee" opacity="0.6"/>
  <rect x="70" y="195" width="130" height="4" fill="#84cc16" opacity="0.6"/>
  
  <!-- Floating elements -->
  <circle cx="320" cy="80" r="15" fill="url(#grad1)" opacity="0.7">
    <animate attributeName="cy" values="80;90;80" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="80" cy="220" r="10" fill="url(#grad1)" opacity="0.5">
    <animate attributeName="cx" values="80;90;80" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="350" cy="200" r="8" fill="url(#grad1)" opacity="0.6">
    <animate attributeName="cy" values="200;210;200" dur="2.5s" repeatCount="indefinite"/>
  </circle>
</svg>
