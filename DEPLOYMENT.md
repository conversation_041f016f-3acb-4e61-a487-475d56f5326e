# Firebase Deployment Setup for HDCode.dev

This repository contains a complete Firebase Hosting deployment setup for the HDCode.dev Hugo static site.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm
- Hugo (latest version)
- Firebase account with a project set up

### Initial Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Login to Firebase:**
   ```bash
   npm run firebase:login
   ```

3. **Update configuration:**
   Edit `deployment-config.json` to match your Firebase project settings:
   ```json
   {
     "firebase": {
       "projectId": "your-firebase-project-id",
       "hostingSite": "your-hosting-site-name"
     }
   }
   ```

4. **Deploy your site:**
   ```bash
   npm run deploy
   ```

## 📁 Configuration Files

### `deployment-config.json`
Central configuration file for all deployment settings:
- **firebase**: Firebase project and hosting configuration
- **hugo**: Hugo build settings and output directory
- **deployment**: Deployment behavior options
- **notifications**: Control what information is displayed during deployment

### `firebase.json`
Firebase Hosting configuration with:
- Static file serving from `public/` directory
- Caching headers for optimal performance
- Clean URLs and trailing slash handling
- CORS headers for fonts

### `.firebaserc`
Firebase project configuration specifying which Firebase project to deploy to.

### `package.json`
Node.js project configuration with deployment scripts and Firebase CLI dependency.

## 🛠️ Deployment Scripts

### Main Deployment Script (`deploy.js`)
Comprehensive Node.js script that:
- ✅ Checks all prerequisites (Hugo, Firebase CLI, authentication)
- 🧹 Optionally cleans previous builds
- 🏗️ Builds the Hugo site with minification
- 🚀 Deploys to Firebase Hosting
- 📊 Provides detailed status feedback and error handling
- ⏱️ Shows deployment timing and success URLs

### Shell Wrapper (`deploy.sh`)
Simple bash wrapper that:
- Checks for Node.js and npm
- Installs dependencies if needed
- Runs the main deployment script

## 📋 Available Commands

### Deployment Commands
```bash
# Full deployment (build + deploy)
npm run deploy

# Quick deployment (skip Hugo build)
npm run deploy:quick

# Custom deployment message
node deploy.js --message="Your custom message"

# Skip build step
node deploy.js --skip-build
```

### Development Commands
```bash
# Build Hugo site locally
npm run build

# Serve Hugo site locally
npm run serve
```

### Firebase Management Commands
```bash
# Login to Firebase
npm run firebase:login

# Logout from Firebase
npm run firebase:logout

# List Firebase projects
npm run firebase:projects

# List hosting sites
npm run firebase:hosting
```

## 🔧 Customization

### Deployment Configuration
Edit `deployment-config.json` to customize:

```json
{
  "firebase": {
    "projectId": "your-project-id",
    "hostingSite": "your-site-name",
    "region": "us-central1"
  },
  "hugo": {
    "buildCommand": "hugo --minify",
    "publicDir": "public",
    "baseURL": "https://your-domain.com"
  },
  "deployment": {
    "message": "Automated deployment",
    "cleanBuild": true,
    "skipBuild": false
  },
  "notifications": {
    "showBuildStatus": true,
    "showDeploymentUrl": true,
    "showTimestamp": true
  }
}
```

### Firebase Hosting Configuration
Edit `firebase.json` to customize:
- Caching headers
- Redirects and rewrites
- Security headers
- File ignore patterns

## 🚨 Troubleshooting

### Common Issues

1. **"Hugo not found"**
   - Install Hugo: https://gohugo.io/installation/
   - Ensure Hugo is in your PATH

2. **"Not authenticated with Firebase"**
   - Run: `npm run firebase:login`
   - Follow the authentication flow

3. **"Project not found"**
   - Update `projectId` in `deployment-config.json`
   - Verify project exists: `npm run firebase:projects`

4. **"Build directory empty"**
   - Check Hugo configuration
   - Ensure content exists in `content/` directory
   - Run `npm run build` to test locally

### Debug Mode
For detailed debugging, run commands directly:
```bash
# Test Hugo build
hugo --minify --verbose

# Test Firebase deployment
npx firebase deploy --only hosting --debug
```

## 🔒 Security Notes

- All configuration files are committed to the repository
- Firebase Hosting configuration keys are public (this is normal)
- No sensitive credentials are stored in these files
- Authentication is handled through Firebase CLI login

## 📈 Performance Features

- **Optimized caching headers** for different file types
- **Clean URLs** without .html extensions
- **Minified Hugo output** for smaller file sizes
- **CORS headers** for web fonts
- **Automatic compression** via Firebase Hosting

## 🎯 Next Steps

After successful deployment:
1. Set up custom domain in Firebase Console (if needed)
2. Configure SSL certificate (automatic with Firebase)
3. Set up monitoring and analytics
4. Consider setting up CI/CD pipeline for automated deployments

---

**Happy Deploying! 🚀**
